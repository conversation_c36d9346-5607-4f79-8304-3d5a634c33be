/**
 * @fileoverview Utility for transforming wizard data to AI-ready format for final recipes generation.
 * Converts collected user data into the format expected by the final-recipes.yaml prompt.
 */

import type {
  HealthConcernData,
  DemographicsData,
  PotentialCause,
  PotentialSymptom,
  PropertyOilSuggestions,
  RecipeTimeSlot
} from '../types/recipe.types';

/**
 * Interface for the transformed data structure expected by AI prompt
 */
export interface RecipeGenerationData {
  health_concern: string;
  gender: string;
  age_category: string;
  specific_age: number;
  selected_causes: Array<{
    cause_id: string;
    cause_name: string;
    explanation: string;
  }>;
  selected_symptoms: Array<{
    symptom_id: string;
    symptom_name: string;
    explanation: string;
  }>;
  suggested_oils: Array<{
    oil_id: string;
    name_english: string;
    name_botanical: string;
    name_localized: string;
    safety: any; // Safety data from enrichment
  }>;
  time_of_day: RecipeTimeSlot;
}

/**
 * Transforms wizard data into AI-ready format for recipe generation
 * 
 * @param healthConcern - User's health concern data
 * @param demographics - User's demographic information
 * @param selectedCauses - Selected potential causes
 * @param selectedSymptoms - Selected symptoms
 * @param suggestedOils - Enriched oil suggestions from properties step
 * @param timeSlot - Time of day for the recipe
 * @returns Transformed data ready for AI prompt
 */
export function transformWizardDataForRecipeGeneration(
  healthConcern: HealthConcernData,
  demographics: DemographicsData,
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[],
  suggestedOils: PropertyOilSuggestions[],
  timeSlot: RecipeTimeSlot
): RecipeGenerationData {
  // Extract all unique oils from property suggestions
  const allOils = suggestedOils.reduce((oils, property) => {
    property.suggested_oils.forEach(oil => {
      // Avoid duplicates by checking oil_id
      if (!oils.find(existingOil => existingOil.oil_id === oil.oil_id)) {
        oils.push({
          oil_id: oil.oil_id,
          name_english: oil.name_english,
          name_botanical: oil.name_botanical,
          name_localized: oil.name_localized,
          safety: oil.safety || {}
        });
      }
    });
    return oils;
  }, [] as RecipeGenerationData['suggested_oils']);

  return {
    health_concern: healthConcern.healthConcern,
    gender: demographics.gender,
    age_category: demographics.ageCategory,
    specific_age: demographics.specificAge,
    selected_causes: selectedCauses.map(cause => ({
      cause_id: cause.cause_id,
      cause_name: cause.cause_name,
      explanation: cause.explanation
    })),
    selected_symptoms: selectedSymptoms.map(symptom => ({
      symptom_id: symptom.symptom_id,
      symptom_name: symptom.symptom_name,
      explanation: symptom.explanation
    })),
    suggested_oils: allOils,
    time_of_day: timeSlot
  };
}

/**
 * Creates a stream request for final recipe generation
 * Following the pattern from existing create-stream-request utilities
 * 
 * @param timeSlot - Time of day for the recipe
 * @param wizardData - Transformed wizard data
 * @param userLanguage - User's language preference
 * @returns Stream request object
 */
export function createFinalRecipeStreamRequest(
  timeSlot: RecipeTimeSlot,
  wizardData: RecipeGenerationData,
  userLanguage: string
) {
  return {
    feature: 'create-recipe',
    step: 'final-recipes',
    dataKeys: ['recipe_protocol'],
    templateVariables: {
      health_concern: wizardData.health_concern,
      gender: wizardData.gender,
      age_category: wizardData.age_category,
      specific_age: wizardData.specific_age,
      selected_causes: wizardData.selected_causes,
      selected_symptoms: wizardData.selected_symptoms,
      suggested_oils: wizardData.suggested_oils,
      time_of_day: timeSlot
    },
    userLanguage,
    metadata: {
      timeSlot,
      totalOils: wizardData.suggested_oils.length,
      userAge: wizardData.specific_age,
      isChild: wizardData.specific_age < 10
    }
  };
}

/**
 * Validates that all required data is present for recipe generation
 * 
 * @param healthConcern - Health concern data
 * @param demographics - Demographics data
 * @param selectedCauses - Selected causes
 * @param selectedSymptoms - Selected symptoms
 * @param suggestedOils - Oil suggestions
 * @returns Validation result with error messages if any
 */
export function validateRecipeGenerationData(
  healthConcern: HealthConcernData | null,
  demographics: DemographicsData | null,
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[],
  suggestedOils: PropertyOilSuggestions[]
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!healthConcern?.healthConcern) {
    errors.push('Health concern is required');
  }

  if (!demographics?.gender) {
    errors.push('Gender is required');
  }

  if (!demographics?.ageCategory) {
    errors.push('Age category is required');
  }

  if (typeof demographics?.specificAge !== 'number' || demographics.specificAge < 0) {
    errors.push('Valid specific age is required');
  }

  if (selectedCauses.length === 0) {
    errors.push('At least one cause must be selected');
  }

  if (selectedSymptoms.length === 0) {
    errors.push('At least one symptom must be selected');
  }

  if (suggestedOils.length === 0) {
    errors.push('Oil suggestions are required');
  }

  // Check if oils have been enriched with safety data
  const hasEnrichedOils = suggestedOils.some(property => 
    property.suggested_oils.some(oil => oil.isEnriched)
  );

  if (!hasEnrichedOils) {
    errors.push('Oils must be enriched with safety data before recipe generation');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Extracts oil IDs from property suggestions for easy access
 * 
 * @param suggestedOils - Property oil suggestions
 * @returns Array of unique oil IDs
 */
export function extractOilIds(suggestedOils: PropertyOilSuggestions[]): string[] {
  const oilIds = new Set<string>();
  
  suggestedOils.forEach(property => {
    property.suggested_oils.forEach(oil => {
      oilIds.add(oil.oil_id);
    });
  });

  return Array.from(oilIds);
}

/**
 * Extracts property IDs from therapeutic properties for context
 * 
 * @param suggestedOils - Property oil suggestions
 * @returns Array of property IDs
 */
export function extractPropertyIds(suggestedOils: PropertyOilSuggestions[]): string[] {
  return suggestedOils.map(property => property.property_id);
}
