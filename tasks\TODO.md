# Create Recipe Final Step Implementation - TODO

## Analysis Summary

### Documentation Issues Found
- [ ] **CRITICAL**: Documentation files in `docs/create-recipe/readme/` have encoding issues (UTF-16 with BOM)
  - `ai-streaming-architecture.md` - Unreadable due to encoding
  - `dashboard-integration-guide.md` - Unreadable due to encoding
  - `development-guide.md` - Unreadable due to encoding
  - `troubleshooting-guide.md` - Readable but has encoding artifacts

### Architecture Understanding
- [x] **Current State**: Recipe wizard has 5 steps (HEALTH_CONCERN, DEMOGRAPHICS, CAUSES, SYMPTOMS, PROPERTIES)
- [x] **Missing**: FINAL_RECIPES step needs to be added to replace the unused OILS step
- [x] **Patterns**: Uses OpenAI Agents JS SDK with centralized hook architecture
  - Single hooks for simple steps (`useAIStreaming`)
  - Parallel hooks for complex steps (`useCreateRecipeStreaming` → `useParallelStreamingEngine`)
- [x] **Data Flow**: YAML prompts → AI Response → Response Parser → Zustand Store → React Components

### Implementation Requirements
- [x] **Goal**: Add FINAL_RECIPES step that generates 3 parallel recipes (morning/mid-day/night)
- [x] **Technology**: OpenAI Agents JS SDK with `gpt-4.1-nano` model
- [x] **Safety**: Filter dermocaustic oils for children under 10
- [x] **UI**: Follow `standalone-v1.html` design with theme variables only
- [x] **State**: Session-only storage (no database persistence for MVP)

## Implementation Plan

### Phase 1: Core Architecture Updates
- [x] 1.1 Add FINAL_RECIPES enum to RecipeStep in types/recipe.types.ts
- [x] 1.2 Create new interfaces for final step data structures
- [x] 1.3 Update WIZARD_STEPS constant to include final step
- [x] 1.4 Update wizard-container.tsx to render final step component

### Phase 2: AI Integration System
- [x] 2.1 Create final-recipes.yaml prompt template
- [x] 2.2 Implement recipe data transformer utility
- [x] 2.3 Create safety filter utility for children under 10
- [ ] 2.4 Implement use-final-recipes-generation hook for parallel AI calls
- [ ] 2.5 Add error handling and retry logic

### Phase 3: Frontend Components
- [ ] 3.1 Create final-recipes-display.tsx main container
- [ ] 3.2 Create recipe-protocol-card.tsx presentational component
- [ ] 3.3 Create container-recommendation.tsx component
- [ ] 3.4 Create safety-warnings.tsx component
- [ ] 3.5 Implement responsive layout with theme variables

### Phase 4: State Management
- [ ] 4.1 Extend Zustand store with final recipes state
- [ ] 4.2 Update navigation logic to prevent back navigation from final step
- [ ] 4.3 Add granular loading states for each recipe protocol

### Phase 5: Internationalization & Testing
- [ ] 5.1 Add translation keys for all final step UI text
- [ ] 5.2 Configure AI prompt for _localized key strategy
- [ ] 5.3 Write comprehensive unit tests for all components
- [ ] 5.4 Test parallel AI generation with mock data

### Phase 6: Documentation Updates
- [ ] 6.1 Fix encoding issues in documentation files
- [ ] 6.2 Update architecture documentation to reflect final implementation
- [ ] 6.3 Update task files with completion status

## Key Technical Decisions

### Architecture Consistency
- **Hook Pattern**: Use centralized hook architecture (one main hook vs multiple hooks)
- **Streaming**: Support parallel API calls using `useParallelStreamingEngine` pattern
- **State Management**: Extend existing Zustand store following established patterns
- **Component Structure**: Keep components under 500 lines following DRY/KISS principles

### Safety Implementation
- **Child Safety**: Filter dermocaustic oils for users under 10 years old
- **Dilution Ratios**: Apply conservative dilution based on demographic data
- **Container Sizing**: Calculate based on usage frequency and application method

### Performance Optimization
- **Parallel Generation**: Generate 3 recipes simultaneously using parallel streaming
- **Retry Logic**: Up to 2 retries per failed recipe with exponential backoff
- **Progressive Loading**: Stream responses for real-time user feedback

## Next Steps
1. Start with Phase 1 (Core Architecture Updates)
2. Implement each phase sequentially
3. Test thoroughly at each phase
4. Update documentation as implementation progresses

## Notes
- Follow existing patterns in `src/features/create-recipe` for consistency
- Use `gpt-4.1-nano` model as specified by user preferences
- Maintain exact UI/UX consistency with existing steps
- Ensure 100% i18n compliance with proper namespace syntax