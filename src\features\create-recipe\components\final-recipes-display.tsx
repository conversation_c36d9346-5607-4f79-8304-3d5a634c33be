/**
 * @fileoverview Final recipes display component - main container with tab navigation
 * Shows three time-specific essential oil protocols with overview and detailed views
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRecipeStore } from '../store/recipe-store';
import { useCreateRecipeStreaming } from '../hooks/use-create-recipe-streaming';
import { useBatchedRecipeUpdates } from '../hooks/use-batched-recipe-updates';
import { RecipeTimeSlot } from '../types/recipe.types';
import { ProtocolSummaryCard } from './protocol-summary-card';
import { RecipeProtocolCard } from './recipe-protocol-card';
import { SafetyWarnings } from './safety-warnings';
import { useI18n } from '@/hooks/use-i18n';

/**
 * Tab types for navigation
 */
type TabType = 'overview' | 'recipes' | 'studies' | 'security';

/**
 * Main final recipes display component with tab navigation
 * Follows the exact structure from standalone-v1.html
 */
export function FinalRecipesDisplay() {
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [activeProtocol, setActiveProtocol] = useState<RecipeTimeSlot>('morning');
  const { t } = useI18n();

  // Store state
  const {
    healthConcern,
    demographics,
    selectedCauses,
    selectedSymptoms,
    suggestedOils,
    finalRecipes,
    isStreamingFinalRecipes
  } = useRecipeStore();

  // Streaming hooks
  const { startFinalRecipesStreaming } = useCreateRecipeStreaming();
  const { startAIStreaming, completeAIStreaming } = useBatchedRecipeUpdates();

  // Auto-start recipe generation when component mounts
  useEffect(() => {
    if (healthConcern && demographics && selectedCauses.length > 0 &&
        selectedSymptoms.length > 0 && suggestedOils.length > 0 &&
        !finalRecipes.hasStartedGeneration) {

      handleGenerateRecipes();
    }
  }, [healthConcern, demographics, selectedCauses, selectedSymptoms, suggestedOils]);

  /**
   * Generate all three recipe protocols
   */
  const handleGenerateRecipes = async () => {
    if (!healthConcern || !demographics) {
      console.warn('Missing required data for recipe generation');
      return;
    }

    // Validate required data
    if (selectedCauses.length === 0 || selectedSymptoms.length === 0 || suggestedOils.length === 0) {
      console.warn('Insufficient wizard data for recipe generation');
      return;
    }

    try {
      console.log('🍃 [Final Recipes] Starting recipe generation process');
      startAIStreaming('final-recipes');

      const timeSlots: RecipeTimeSlot[] = ['morning', 'mid-day', 'night'];
      const results = await startFinalRecipesStreaming(
        timeSlots,
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        suggestedOils
      );

      console.log('🍃 [Final Recipes] Processing results:', results.size, 'recipes generated');

      // Process results and update store with individual recipes
      if (results.size > 0) {
        results.forEach((recipe, timeSlot) => {
          if (recipe) {
            console.log(`✅ [Final Recipes] Updating store with ${timeSlot} recipe`);
            // The results will be processed by completeAIStreaming
          }
        });
      }

      completeAIStreaming('final-recipes', results);

    } catch (error) {
      console.error('Failed to generate final recipes:', error);
      completeAIStreaming('final-recipes', new Map());
    }
  };

  /**
   * Switch between tabs
   */
  const switchTab = (tab: TabType) => {
    setActiveTab(tab);
  };

  /**
   * Switch between protocol timelines in recipes tab
   */
  const switchProtocol = (protocol: RecipeTimeSlot) => {
    setActiveProtocol(protocol);
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* Tab Navigation */}
      <nav className="flex gap-6 border-b-2 border-slate-200 mb-8 sticky top-0 z-10 bg-white/80 backdrop-blur-sm">
        <TabButton
          active={activeTab === 'overview'}
          onClick={() => switchTab('overview')}
          icon={<InfoIcon />}
          label={t('create-recipe:steps.final-recipes.tabs.overview')}
        />
        <TabButton
          active={activeTab === 'recipes'}
          onClick={() => switchTab('recipes')}
          icon={<FlaskIcon />}
          label={t('create-recipe:steps.final-recipes.tabs.recipes')}
        />
        <TabButton
          active={activeTab === 'studies'}
          onClick={() => switchTab('studies')}
          icon={<DocumentIcon />}
          label={t('create-recipe:steps.final-recipes.tabs.studies')}
        />
        <TabButton
          active={activeTab === 'security'}
          onClick={() => switchTab('security')}
          icon={<ShieldIcon />}
          label={t('create-recipe:steps.final-recipes.tabs.security')}
        />
      </nav>

      {/* Tab Content */}
      <main>
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <OverviewTab
            healthConcern={healthConcern}
            demographics={demographics}
            selectedCauses={selectedCauses}
            selectedSymptoms={selectedSymptoms}
            finalRecipes={finalRecipes}
            isLoading={isStreamingFinalRecipes}
            onSwitchToRecipes={() => switchTab('recipes')}
          />
        )}

        {/* Recipes Tab */}
        {activeTab === 'recipes' && (
          <RecipesTab
            activeProtocol={activeProtocol}
            onSwitchProtocol={switchProtocol}
            finalRecipes={finalRecipes}
            isLoading={isStreamingFinalRecipes}
          />
        )}

        {/* Studies Tab */}
        {activeTab === 'studies' && (
          <StudiesTab />
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          <SecurityTab demographics={demographics} />
        )}
      </main>
    </div>
  );
}

/**
 * Tab button component
 */
interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
}

function TabButton({ active, onClick, icon, label }: TabButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`
        flex items-center gap-2 px-1 py-3 border-none bg-none cursor-pointer
        text-base font-medium border-b-2 transform translate-y-0.5
        transition-all duration-200 ease-in-out
        ${active
          ? 'text-teal-600 font-semibold border-teal-600'
          : 'text-slate-500 border-transparent hover:text-teal-600'
        }
      `}
    >
      <span className="h-5 w-5">{icon}</span>
      {label}
    </button>
  );
}

/**
 * Overview tab component
 */
interface OverviewTabProps {
  healthConcern: any;
  demographics: any;
  selectedCauses: any[];
  selectedSymptoms: any[];
  finalRecipes: any;
  isLoading: boolean;
  onSwitchToRecipes: () => void;
}

function OverviewTab({
  healthConcern,
  demographics,
  selectedCauses,
  selectedSymptoms,
  finalRecipes,
  isLoading,
  onSwitchToRecipes
}: OverviewTabProps) {
  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
        {/* User Profile */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl p-6 shadow-lg h-full">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <UserIcon className="h-6 w-6 text-teal-600" />
              {t('create-recipe:steps.final-recipes.overview.userProfile.title')}
            </h2>
            <div className="space-y-3 text-gray-600">
              <div className="flex justify-between">
                <strong>{t('create-recipe:steps.final-recipes.overview.userProfile.condition')}:</strong>
                <span>{healthConcern?.healthConcern || t('create-recipe:steps.final-recipes.overview.userProfile.notProvided')}</span>
              </div>
              <div className="flex justify-between">
                <strong>{t('create-recipe:steps.final-recipes.overview.userProfile.age')}:</strong>
                <span>{demographics?.specificAge || t('create-recipe:steps.final-recipes.overview.userProfile.notProvided')} anos</span>
              </div>
              <div className="flex justify-between">
                <strong>{t('create-recipe:steps.final-recipes.overview.userProfile.gender')}:</strong>
                <span>{demographics?.gender || t('create-recipe:steps.final-recipes.overview.userProfile.notProvided')}</span>
              </div>

              {selectedCauses.length > 0 && (
                <div className="pt-2">
                  <h3 className="font-semibold text-gray-700 mb-2">{t('create-recipe:steps.final-recipes.overview.userProfile.identifiedCauses')}:</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedCauses.map((cause, index) => (
                      <span key={index} className="inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        {cause.cause_name}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {selectedSymptoms.length > 0 && (
                <div className="pt-2">
                  <h3 className="font-semibold text-gray-700 mb-2">{t('create-recipe:steps.final-recipes.overview.userProfile.symptoms')}:</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedSymptoms.map((symptom, index) => (
                      <span key={index} className="inline-block px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        {symptom.symptom_name}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Therapeutic Strategy */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-2xl p-6 shadow-lg h-full">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <ChartIcon className="h-6 w-6 text-teal-600" />
              {t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.title')}
            </h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-700 mb-3">{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.properties')}:</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <div className="bg-teal-50 p-3 rounded-lg">
                    <strong className="text-teal-800">Calmante</strong>
                    <p className="text-sm text-teal-700">Reduz ansiedade</p>
                  </div>
                  <div className="bg-teal-50 p-3 rounded-lg">
                    <strong className="text-teal-800">Relaxante</strong>
                    <p className="text-sm text-teal-700">Alivia tensão</p>
                  </div>
                  <div className="bg-teal-50 p-3 rounded-lg">
                    <strong className="text-teal-800">Anti-stress</strong>
                    <p className="text-sm text-teal-700">Gestão emocional</p>
                  </div>
                  <div className="bg-teal-50 p-3 rounded-lg">
                    <strong className="text-teal-800">Indutor do sono</strong>
                    <p className="text-sm text-teal-700">Melhora descanso</p>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-gray-700 mb-2">{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.applicationMethods')}:</h3>
                <div className="flex flex-wrap gap-3 text-gray-600">
                  <span>{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.topical')}</span>
                  <span>{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.aromatic')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Protocol Summary Cards */}
      <div className="bg-white rounded-2xl p-6 shadow-lg">
        <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          <ClipboardIcon className="h-6 w-6 text-teal-600" />
          {t('create-recipe:steps.final-recipes.overview.protocolSummary.title')}
        </h2>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {['morning', 'mid-day', 'night'].map((timeSlot) => (
              <div key={timeSlot} className="h-96 bg-gray-100 rounded-2xl animate-pulse flex items-center justify-center">
                <div className="text-gray-500">{t('create-recipe:steps.final-recipes.loading')}</div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <ProtocolSummaryCard
              timeSlot="morning"
              recipe={finalRecipes.morning.recipe}
              onViewDetails={() => onSwitchToRecipes()}
            />
            <ProtocolSummaryCard
              timeSlot="mid-day"
              recipe={finalRecipes.midDay.recipe}
              onViewDetails={() => onSwitchToRecipes()}
            />
            <ProtocolSummaryCard
              timeSlot="night"
              recipe={finalRecipes.night.recipe}
              onViewDetails={() => onSwitchToRecipes()}
            />
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Recipes tab component
 */
interface RecipesTabProps {
  activeProtocol: RecipeTimeSlot;
  onSwitchProtocol: (protocol: RecipeTimeSlot) => void;
  finalRecipes: any;
  isLoading: boolean;
}

function RecipesTab({ activeProtocol, onSwitchProtocol, finalRecipes, isLoading }: RecipesTabProps) {
  const { t } = useI18n();
  const timeSlots = [
    {
      key: 'morning' as RecipeTimeSlot,
      label: t('create-recipe:steps.final-recipes.protocols.morning.label'),
      time: t('create-recipe:steps.final-recipes.protocols.morning.timeRange'),
      description: t('create-recipe:steps.final-recipes.protocols.morning.description')
    },
    {
      key: 'mid-day' as RecipeTimeSlot,
      label: t('create-recipe:steps.final-recipes.protocols.midDay.label'),
      time: t('create-recipe:steps.final-recipes.protocols.midDay.timeRange'),
      description: t('create-recipe:steps.final-recipes.protocols.midDay.description')
    },
    {
      key: 'night' as RecipeTimeSlot,
      label: t('create-recipe:steps.final-recipes.protocols.night.label'),
      time: t('create-recipe:steps.final-recipes.protocols.night.timeRange'),
      description: t('create-recipe:steps.final-recipes.protocols.night.description')
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-8 px-2 sm:px-4 md:px-0">
      {/* Timeline Navigation */}
      <aside className="md:col-span-1 flex flex-col items-center md:items-stretch">
        <div className="relative flex flex-col h-full">
          {timeSlots.map((slot, index) => (
            <div
              key={slot.key}
              className={`
                relative pl-10 pb-8 cursor-pointer transition-all duration-200
                ${activeProtocol === slot.key ? 'active' : ''}
              `}
              onClick={() => onSwitchProtocol(slot.key)}
            >
              {/* Timeline dot */}
              <div className={`
                absolute left-4 top-6 transform -translate-x-1/2 w-5 h-5 rounded-full
                border-3 bg-white transition-all duration-300
                ${activeProtocol === slot.key
                  ? 'border-teal-600 bg-teal-600'
                  : 'border-slate-300'
                }
              `} />

              {/* Timeline line */}
              {index < timeSlots.length - 1 && (
                <div className="absolute left-4 top-11 bottom-0 w-0.5 bg-slate-300 transform -translate-x-1/2" />
              )}

              {/* Content */}
              <div className={`
                p-4 rounded-lg transition-all duration-200
                ${activeProtocol === slot.key
                  ? 'bg-teal-50 border-2 border-teal-200 shadow-md'
                  : 'bg-teal-100 hover:bg-teal-50'
                }
              `}>
                <p className="text-sm text-teal-800">{slot.time}</p>
                <h4 className="font-bold text-teal-900">{slot.label}</h4>
                <p className="text-sm text-teal-700">{slot.description}</p>
              </div>
            </div>
          ))}
        </div>
      </aside>

      {/* Protocol Cards Container */}
      <section className="md:col-span-3 flex flex-col items-center gap-8 w-full">
        {isLoading ? (
          <div className="w-full max-w-4xl h-96 bg-gray-100 rounded-2xl animate-pulse flex items-center justify-center">
            <div className="text-gray-500">{t('create-recipe:steps.final-recipes.loading')}</div>
          </div>
        ) : (
          <RecipeProtocolCard
            timeSlot={activeProtocol}
            recipe={finalRecipes[activeProtocol === 'mid-day' ? 'midDay' : activeProtocol]?.recipe}
          />
        )}
      </section>
    </div>
  );
}

/**
 * Studies tab component
 */
function StudiesTab() {
  const { t } = useI18n();
  return (
    <div className="bg-white rounded-2xl p-6 shadow-lg">
      <h2 className="text-xl font-bold text-gray-800 mb-4">{t('create-recipe:steps.final-recipes.studies.title')}</h2>
      <p className="text-gray-600">
        {t('create-recipe:steps.final-recipes.studies.description')}
      </p>
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <p className="text-blue-800 text-sm">
          🔬 <strong>Em desenvolvimento:</strong> {t('create-recipe:steps.final-recipes.studies.inDevelopment')}
        </p>
      </div>
    </div>
  );
}

/**
 * Security tab component
 */
interface SecurityTabProps {
  demographics: any;
}

function SecurityTab({ demographics }: SecurityTabProps) {
  const { t } = useI18n();
  return (
    <div className="space-y-6">
      <SafetyWarnings demographics={demographics} />

      <div className="bg-white rounded-2xl p-6 shadow-lg">
        <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          <ShieldIcon className="h-6 w-6 text-teal-600" />
          {t('create-recipe:steps.final-recipes.safety.title')}
        </h2>
        <div className="space-y-4 text-gray-700">
          <div>
            <h3 className="font-semibold mb-2">{t('create-recipe:steps.final-recipes.safety.guidelines.duringUse.title')}</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>{t('create-recipe:steps.final-recipes.safety.guidelines.beforeUse.patchTest')}</li>
              <li>{t('create-recipe:steps.final-recipes.safety.guidelines.beforeUse.checkAllergies')}</li>
              <li>{t('create-recipe:steps.final-recipes.safety.guidelines.duringUse.avoidEyes')}</li>
              <li>{t('create-recipe:steps.final-recipes.safety.guidelines.duringUse.noOpenWounds')}</li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('create-recipe:steps.final-recipes.safety.guidelines.storage.title')}</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>{t('create-recipe:steps.final-recipes.safety.guidelines.storage.coolDry')}</li>
              <li>{t('create-recipe:steps.final-recipes.safety.guidelines.storage.protectLight')}</li>
              <li>{t('create-recipe:steps.final-recipes.safety.guidelines.storage.keepAway')}</li>
              <li>{t('create-recipe:steps.final-recipes.safety.guidelines.storage.darkGlass')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// ICON COMPONENTS
// ============================================================================

function InfoIcon() {
  return (
    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );
}

function FlaskIcon() {
  return (
    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
    </svg>
  );
}

function DocumentIcon() {
  return (
    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 20h9" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16.5 3.5a2.121 2.121 0 013 3L7 19.5 3 21l1.5-4L16.5 3.5z" />
    </svg>
  );
}

function ShieldIcon() {
  return (
    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  );
}

function UserIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
      <path strokeLinecap="round" strokeLinejoin="round" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0m-6 4a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );
}

function ChartIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2m0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  );
}

function ClipboardIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
    </svg>
  );
}
